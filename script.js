// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Header scroll effect
window.addEventListener('scroll', () => {
    const header = document.querySelector('header');
    if (window.scrollY > 100) {
        header.style.background = 'rgba(46, 55, 63, 0.98)';
        header.style.boxShadow = '0 2px 20px rgba(0,0,0,0.1)';
    } else {
        header.style.background = 'rgba(46, 55, 63, 0.95)';
        header.style.boxShadow = 'none';
    }
});

// Intersection Observer for fade-in animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('visible');
        }
    });
}, observerOptions);

// Observe all fade-in elements
document.querySelectorAll('.fade-in').forEach(el => {
    observer.observe(el);
});

// Add interactive hover effects to gallery items
document.querySelectorAll('.gallery-item').forEach(item => {
    item.addEventListener('mouseenter', function() {
        this.style.transform = 'scale(1.05) rotateY(5deg)';
    });
    
    item.addEventListener('mouseleave', function() {
        this.style.transform = 'scale(1) rotateY(0deg)';
    });
});

// Add click effects to buttons
document.querySelectorAll('.btn').forEach(btn => {
    btn.addEventListener('click', function(e) {
        // Create ripple effect
        const ripple = document.createElement('span');
        const rect = this.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;
        
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        `;
        
        this.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    });
});

// Add CSS animation for ripple effect
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(2);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// Add parallax effect to hero section
window.addEventListener('scroll', () => {
    const scrolled = window.pageYOffset;
    const hero = document.querySelector('.hero');
    if (hero) {
        hero.style.transform = `translateY(${scrolled * 0.5}px)`;
    }
});

// Loading animation
window.addEventListener('load', () => {
    document.body.style.opacity = '0';
    setTimeout(() => {
        document.body.style.transition = 'opacity 0.5s ease';
        document.body.style.opacity = '1';
    }, 100);
});

// Gallery horizontal scroll functionality
document.addEventListener('DOMContentLoaded', function() {
    let currentSlide = 0;
    const galleryGrid = document.getElementById('galleryGrid');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const dots = document.querySelectorAll('.gallery-dots .dot');
    const totalSlides = 4;

    if (!galleryGrid || !prevBtn || !nextBtn || dots.length === 0) {
        console.log('Gallery elements not found');
        return;
    }

    function updateGallery() {
        const cardWidth = 350;
        const gap = 30;
        const translateX = -currentSlide * (cardWidth + gap);
        
        galleryGrid.style.transform = `translateX(${translateX}px)`;
        
        dots.forEach((dot, index) => {
            dot.classList.toggle('active', index === currentSlide);
        });
    }

    // Auto-scroll functionality
    function autoScroll() {
        currentSlide = currentSlide < totalSlides - 1 ? currentSlide + 1 : 0;
        updateGallery();
    }

    // Start auto-scrolling every 3 seconds
    let autoScrollInterval = setInterval(autoScroll, 3000);

    // Optional: Keep manual controls but pause auto-scroll when user interacts
    prevBtn.addEventListener('click', function(e) {
        e.preventDefault();
        clearInterval(autoScrollInterval);
        currentSlide = currentSlide > 0 ? currentSlide - 1 : totalSlides - 1;
        updateGallery();
        autoScrollInterval = setInterval(autoScroll, 3000);
    });

    nextBtn.addEventListener('click', function(e) {
        e.preventDefault();
        clearInterval(autoScrollInterval);
        currentSlide = currentSlide < totalSlides - 1 ? currentSlide + 1 : 0;
        updateGallery();
        autoScrollInterval = setInterval(autoScroll, 3000);
    });

    dots.forEach((dot, index) => {
        dot.addEventListener('click', function() {
            clearInterval(autoScrollInterval);
            currentSlide = index;
            updateGallery();
            autoScrollInterval = setInterval(autoScroll, 3000);
        });
    });

    updateGallery();
});



